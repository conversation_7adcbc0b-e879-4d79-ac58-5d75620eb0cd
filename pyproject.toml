[tool.ruff]
line-length = 120
exclude = ['tools']
# A list of file patterns to omit from linting, in addition to those specified by exclude.
extend-exclude = ["__pycache__", "*.pyc", "*.egg-info", ".cache"]

select = ["E", "F", "W", "C90", "I", "UP", "B", "C4", "RET", "RUF", "SIM"]


ignore = [
    "UP006",    # UP006: Use list instead of typing.List for type annotations
    "UP007",    # UP007: Use X | Y for type annotations
    "UP009",
    "UP035",
    "UP038",
    "E402",
    "RET504",
    "C901",
    "RUF013",
    "B006",
]

[tool.isort]
profile = "black"

[tool.black]
line-length = 119
skip-string-normalization = 1
exclude = 'tools'